import type { TSESTree } from '@typescript-eslint/utils';
/**
 * Returns the result of the string conversion applied to the evaluated value of the given expression node,
 * if it can be determined statically.
 *
 * This function returns a `string` value for all `Literal` nodes and simple `TemplateLiteral` nodes only.
 * In all other cases, this function returns `null`.
 * @param node Expression node.
 * @returns String value if it can be determined. Otherwise, `null`.
 */
export declare function getStaticStringValue(node: TSESTree.Node): string | null;
//# sourceMappingURL=getStaticStringValue.d.ts.map