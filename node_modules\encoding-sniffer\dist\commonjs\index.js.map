{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,2CAAgE;AAChE,0DAA+B;AAE/B,2CAAoD;AAEpD;;;;;;GAMG;AACH,SAAgB,YAAY,CACxB,MAAc,EACd,OAA4B;IAA5B,wBAAA,EAAA,YAA4B;IAE5B,OAAO,oBAAK,CAAC,MAAM,CAAC,MAAM,EAAE,IAAA,wBAAW,EAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;AAC9D,CAAC;AALD,oCAKC;AAED;;;;;;GAMG;AACH;IAAkC,gCAAS;IAQvC,sBAAY,OAAwB;;QAChC,YAAA,MAAK,YAAC,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,SAAC;QAPtC,aAAO,GAAiB,EAAE,CAAC;QAC5C,8FAA8F;QACtF,WAAK,GAAkC,IAAI,CAAC;QAE5C,eAAS,GAAG,CAAC,CAAC;QAIlB,KAAI,CAAC,OAAO,GAAG,IAAI,oBAAO,CAAC,OAAO,CAAC,CAAC;QACpC,KAAI,CAAC,QAAQ,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,mCAAI,IAAI,CAAC;;IAC9C,CAAC;IAEQ,iCAAU,GAAnB,UACI,KAAiB,EACjB,SAAiB,EACjB,QAA2B;QAE3B,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1B,IAAI,CAAC,SAAS,IAAI,KAAK,CAAC,MAAM,CAAC;YAE/B,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACjC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACzB,QAAQ,EAAE,CAAC;gBACX,OAAO;YACX,CAAC;QACL,CAAC;QAED,IAAI,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IACjD,CAAC;IAEO,qCAAc,GAAtB;QAAA,iBAiBC;QAhBG,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,OAAO,IAAI,CAAC,KAAK,CAAC;QACtB,CAAC;QAED,IAAM,MAAM,GAAG,oBAAK,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACzD,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,KAAa,IAAK,OAAA,KAAI,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,EAAzB,CAAyB,CAAC,CAAC;QAChE,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,cAAM,OAAA,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAf,CAAe,CAAC,CAAC;QAExC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC;QAEpB,KAAqB,UAAY,EAAZ,KAAA,IAAI,CAAC,OAAO,EAAZ,cAAY,EAAZ,IAAY,EAAE,CAAC;YAA/B,IAAM,MAAM,SAAA;YACb,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACzB,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;QAExB,OAAO,MAAM,CAAC;IAClB,CAAC;IAEQ,6BAAM,GAAf,UAAgB,QAA2B;QACvC,IAAI,CAAC,cAAc,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;IACL,mBAAC;AAAD,CAAC,AAvDD,CAAkC,uBAAS,GAuD1C;AAvDY,oCAAY;AAyDzB,2CAAgE;AAAlC,yGAAA,WAAW,OAAA"}