import { Config, Message, Tool<PERSON>all, <PERSON>l<PERSON><PERSON>ult, ExecutionContext, ArienError } from '../types/index.js';
import { createLLMProvider, LLMProvider } from './llm-providers.js';
import { ToolExecutor } from './tool-executor.js';
import { RetryManager } from './retry-logic.js';
import { SYSTEM_PROMPT } from '../config/system-prompt.js';
import { UserInterface } from '../ui/interface.js';

export class ArienAgent {
  private llmProvider: LLMProvider;
  private toolExecutor: ToolExecutor;
  private retryManager: RetryManager;
  private ui: UserInterface;
  private messages: Message[] = [];
  private context: ExecutionContext;

  constructor(config: Config, ui: UserInterface) {
    this.llmProvider = createLLMProvider(config);
    this.toolExecutor = new ToolExecutor();
    this.retryManager = new RetryManager();
    this.ui = ui;
    
    this.context = {
      workingDirectory: process.cwd(),
      environment: process.env as Record<string, string>,
      timeout: config.timeout
    };

    // Initialize with system prompt
    this.messages.push({
      role: 'system',
      content: SYSTEM_PROMPT,
      timestamp: new Date()
    });
  }

  async processUserInput(input: string): Promise<void> {
    try {
      // Add user message
      this.messages.push({
        role: 'user',
        content: input,
        timestamp: new Date()
      });

      // Start thinking animation
      this.ui.startThinking('Processing your request...');

      // Process the conversation with retry logic
      await this.retryManager.executeWithRetry(async () => {
        await this.processConversation();
      }, {
        maxAttempts: 3,
        baseDelay: 2000,
        exponentialBase: 2
      });

    } catch (error) {
      this.ui.stopAnimation();
      
      if (error instanceof ArienError) {
        this.ui.showError(`AI Error: ${error.message}`);
      } else {
        this.ui.showError(`Unexpected error: ${error}`);
      }
    }
  }

  private async processConversation(): Promise<void> {
    let maxIterations = 10; // Prevent infinite loops
    let iteration = 0;

    while (iteration < maxIterations) {
      iteration++;

      try {
        // Get LLM response
        this.ui.startThinking(`Thinking... (iteration ${iteration})`);
        const response = await this.llmProvider.generateResponse(this.messages);
        this.ui.stopAnimation();

        // Show AI response if there's content
        if (response.content && response.content.trim()) {
          this.ui.showAIResponse(response.content);
        }

        // Add assistant message
        this.messages.push({
          role: 'assistant',
          content: response.content,
          toolCalls: response.toolCalls || undefined,
          timestamp: new Date()
        });

        // If no tool calls, we're done
        if (!response.toolCalls || response.toolCalls.length === 0) {
          break;
        }

        // Execute tools
        const toolResults = await this.executeTools(response.toolCalls);

        // Add tool results to conversation
        this.messages.push({
          role: 'tool',
          content: this.formatToolResults(toolResults),
          toolResults,
          timestamp: new Date()
        });

        // Check if all tools succeeded
        const allSucceeded = toolResults.every(result => result.success);
        if (!allSucceeded) {
          // Let the AI know about failures and continue
          const failedTools = toolResults.filter(result => !result.success);
          this.ui.showWarning(`${failedTools.length} tool(s) failed. AI will retry or find alternatives.`);
        }

      } catch (error) {
        this.ui.stopAnimation();
        
        // Add error information to conversation so AI can adapt
        this.messages.push({
          role: 'tool',
          content: `Error occurred: ${error instanceof Error ? error.message : String(error)}`,
          timestamp: new Date()
        });

        // Show error to user but continue processing
        this.ui.showError(`Processing error: ${error}. AI will attempt to recover.`);
      }
    }

    if (iteration >= maxIterations) {
      this.ui.showWarning('Maximum processing iterations reached. The AI may not have completed all tasks.');
    }
  }

  private async executeTools(toolCalls: ToolCall[]): Promise<ToolResult[]> {
    // Determine execution strategy
    const strategy = this.toolExecutor.determineExecutionStrategy(toolCalls);
    const estimatedTime = this.toolExecutor.estimateExecutionTime(toolCalls);

    this.ui.startProgress(`Executing ${toolCalls.length} tool(s) ${strategy}ly...`);

    try {
      let results: ToolResult[];

      if (strategy === 'parallel') {
        this.ui.showInfo(`Executing ${toolCalls.length} tools in parallel (estimated: ${Math.round(estimatedTime/1000)}s)`);
        results = await this.toolExecutor.executeToolsParallel(toolCalls, this.context);
      } else {
        this.ui.showInfo(`Executing ${toolCalls.length} tools sequentially (estimated: ${Math.round(estimatedTime/1000)}s)`);
        results = await this.toolExecutor.executeToolsSequential(toolCalls, this.context);
      }

      this.ui.stopAnimation();

      // Show tool execution results
      results.forEach((result, index) => {
        const toolCall = toolCalls[index];
        if (toolCall) {
          this.ui.showToolResult(
            toolCall.type,
            result.success,
            result.output,
            result.executionTime
          );
        }
      });

      return results;

    } catch (error) {
      this.ui.stopAnimation();
      this.ui.showError(`Tool execution failed: ${error}`);
      
      // Return error results for all tools
      return toolCalls.map(toolCall => ({
        id: toolCall.id,
        success: false,
        output: '',
        error: error instanceof Error ? error.message : String(error),
        executionTime: 0
      }));
    }
  }

  private formatToolResults(results: ToolResult[]): string {
    return results.map(result => {
      if (result.success) {
        return `Tool ${result.id} succeeded:\n${result.output}`;
      } else {
        return `Tool ${result.id} failed: ${result.error}`;
      }
    }).join('\n\n');
  }

  // Utility methods
  getConversationHistory(): Message[] {
    return [...this.messages];
  }

  clearConversation(): void {
    this.messages = [{
      role: 'system',
      content: SYSTEM_PROMPT,
      timestamp: new Date()
    }];
  }

  setWorkingDirectory(directory: string): void {
    this.context.workingDirectory = directory;
    this.ui.showInfo(`Working directory changed to: ${directory}`);
  }

  getWorkingDirectory(): string {
    return this.context.workingDirectory;
  }

  async validateConnection(): Promise<boolean> {
    try {
      return await this.llmProvider.validateConnection();
    } catch {
      return false;
    }
  }

  getToolDescriptions(): Record<string, string> {
    return this.toolExecutor.getAllToolDescriptions();
  }
}
